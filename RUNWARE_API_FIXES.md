# Runware API Integration Fixes - Background Removal

## 📋 Tổng Quan

Tài liệu này ghi lại tất cả các thay đổi được thực hiện để fix chức năng Remove Background với Runware API dựa trên tài liệu ch<PERSON>h thức.

## 🔍 Phân Tích Vấn Đề

### Vấn Đề Trước Khi Fix:
1. **Response Structure**: Code expect `result.imageURL` trực tiếp nhưng Runware API trả về trong `{ data: [...] }` format
2. **Error Handling**: Không xử lý đúng cấu trúc error response từ Runware
3. **Logging**: <PERSON>hi<PERSON><PERSON> logs chi tiết để debug
4. **Task Parameters**: Cần đảm bảo đúng format theo tài liệu API

### Cấu Trúc API Response Thực Tế:
```json
{
  "data": [
    {
      "taskType": "imageBackgroundRemoval",
      "taskUUID": "19abad0d-6ec5-40a6-b7af-203775fa5b7f",
      "imageUUID": "aa418b0f-4b83-4c3d-96c5-30abf4699a4d",
      "inputImageUUID": "fd613011-3872-4f37-b4aa-0d343c051a27",
      "imageURL": "https://im.runware.ai/image/ii/aa418b0f-4b83-4c3d-96c5-30abf4699a4d.jpg",
      "cost": 0.006
    }
  ]
}
```

## 🛠️ Các Thay Đổi Đã Thực Hiện

### 1. File: `src/lib/runware.ts`

#### A. Fix UUID Generation:

**Vấn đề:** `crypto.randomUUID()` có thể không hoạt động trong môi trường Node.js

**Giải pháp:**
```typescript
import { randomUUID } from 'crypto';

// Helper function to generate UUID that works in both browser and Node.js
function generateUUID(): string {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  } else if (typeof randomUUID !== 'undefined') {
    return randomUUID();
  } else {
    // Fallback UUID generation
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
}
```

#### B. Fix Missing TaskUUID:

**Vấn đề:** Các methods `upscaleImage`, `generateBackground`, `enhanceImage` thiếu `taskUUID`

**Giải pháp:** Thêm `taskUUID` cho tất cả methods:
```typescript
// Tất cả methods giờ đều có:
const taskUUID = generateUUID();
const task: RunwareTask = {
  taskType: '...',
  taskUUID: taskUUID,  // ✅ Đã thêm
  inputImage: imageUrl,
  // ... other properties
};
```

#### C. Cải Thiện `makeRequest` Method:

**Trước:**
```typescript
private async makeRequest(tasks: RunwareTask[]): Promise<RunwareResponse[]> {
  const response = await fetch(`${this.baseUrl}/v1`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`,
    },
    body: JSON.stringify(tasks),
  });

  if (!response.ok) {
    throw new Error(`Runware API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  return data;
}
```

**Sau:**
```typescript
private async makeRequest(tasks: RunwareTask[]): Promise<RunwareResponse[]> {
  console.log(`[Runware] Making request to ${this.baseUrl}/v1`);
  console.log(`[Runware] Request payload:`, JSON.stringify(tasks, null, 2));

  const response = await fetch(`${this.baseUrl}/v1`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`,
    },
    body: JSON.stringify(tasks),
  });

  console.log(`[Runware] Response status: ${response.status} ${response.statusText}`);

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`[Runware] Error response:`, errorText);
    throw new Error(`Runware API error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const data = await response.json();
  console.log(`[Runware] Response data:`, JSON.stringify(data, null, 2));

  // Runware API returns data in { data: [...] } format
  if (data.data && Array.isArray(data.data)) {
    return data.data;
  }

  // If there's an error in the response
  if (data.errors && Array.isArray(data.errors)) {
    const errorMessage = data.errors.map((err: any) => err.message).join(', ');
    throw new Error(`Runware API error: ${errorMessage}`);
  }

  // Fallback - return as is if structure is unexpected
  return Array.isArray(data) ? data : [data];
}
```

**Cải thiện:**
- ✅ Thêm logging chi tiết cho request/response
- ✅ Xử lý đúng cấu trúc `{ data: [...] }` response
- ✅ Xử lý errors array từ API
- ✅ Fallback handling cho unexpected response structure

#### B. Cải Thiện `removeBackground` Method:

**Trước:**
```typescript
async removeBackground(imageUrl: string): Promise<string> {
  // ... task creation ...

  try {
    const results = await this.makeRequest([task]);
    const result = results[0];

    if (result.error) {
      throw new Error(`Background removal failed: ${result.error}`);
    }

    if (!result.imageURL) {
      throw new Error('No image URL returned from background removal');
    }

    return result.imageURL;
  } catch (error) {
    throw error;
  }
}
```

**Sau:**
```typescript
async removeBackground(imageUrl: string): Promise<string> {
  // ... task creation ...

  try {
    const results = await this.makeRequest([task]);

    if (!results || results.length === 0) {
      throw new Error('No results returned from Runware API');
    }

    const result = results[0];

    // Check for errors in the result
    if (result.error) {
      console.error(`[Runware] API returned error:`, result.error);
      throw new Error(`Background removal failed: ${result.error}`);
    }

    // Check for imageURL in the response
    if (!result.imageURL) {
      console.error(`[Runware] No imageURL in response:`, result);
      console.error(`[Runware] Available properties:`, Object.keys(result));
      throw new Error('No image URL returned from background removal');
    }

    console.log(`[Runware] Background removal successful. Result URL: ${result.imageURL}`);
    return result.imageURL;
  } catch (error) {
    console.error(`[Runware] Background removal error:`, error);

    // Re-throw with more context if it's our error
    if (error instanceof Error) {
      throw new Error(`Runware background removal failed: ${error.message}`);
    }

    throw new Error('Runware background removal failed: Unknown error');
  }
}
```

**Cải thiện:**
- ✅ Kiểm tra results array không empty
- ✅ Logging chi tiết cho debugging
- ✅ Better error messages với context
- ✅ Log available properties khi không có imageURL

### 2. File: `src/types/index.ts`

Types đã được cập nhật đúng theo tài liệu API:

```typescript
export interface RunwareResponse {
  taskType: string;
  taskUUID: string;
  imageUUID?: string;
  inputImageUUID?: string;
  imageURL?: string;
  imageBase64Data?: string;
  imageDataURI?: string;
  cost?: number;
  error?: string;
}
```

## 🧪 Cách Test Chức Năng

### 1. Kiểm Tra Logs:
```bash
# Terminal chạy npm run dev
[Runware] Making request to https://api.runware.ai/v1
[Runware] Request payload: [{"taskType":"imageBackgroundRemoval",...}]
[Runware] Response status: 200 OK
[Runware] Response data: {"data":[{"taskType":"imageBackgroundRemoval",...}]}
[Runware] Background removal successful. Result URL: https://...
```

### 2. Kiểm Tra Browser Console:
```javascript
// Mở F12 → Console
// Xem logs từ frontend và API calls
```

### 3. Kiểm Tra Network Tab:
```
F12 → Network → POST /api/process/remove-background
- Status: 200
- Response: {"success": true, "data": {...}}
```

## 🔧 Troubleshooting

### Lỗi Thường Gặp:

1. **"No results returned from Runware API"**
   - Kiểm tra API key có đúng không
   - Kiểm tra request format

2. **"No image URL returned from background removal"**
   - Xem logs để kiểm tra available properties
   - Có thể API trả về field khác thay vì `imageURL`

3. **"Runware API error: 401"**
   - API key không hợp lệ hoặc hết hạn
   - Kiểm tra `.env.local`

4. **"Runware API error: 400"**
   - Request format không đúng
   - Kiểm tra task parameters

## 📝 Checklist Sau Khi Fix

- [x] Cập nhật `makeRequest` để handle response structure đúng
- [x] Cải thiện error handling và logging
- [x] Cập nhật `removeBackground` method
- [x] Đảm bảo types đúng theo API documentation
- [x] **FIX UUID Generation** - Thêm helper function cho cross-platform UUID
- [x] **FIX Missing TaskUUID** - Thêm taskUUID cho tất cả methods
- [x] **FIX upscaleImage** - Thêm taskUUID và logging
- [x] **FIX generateBackground** - Thêm taskUUID và logging
- [x] **FIX enhanceImage** - Thêm taskUUID và logging
- [x] Tạo documentation chi tiết
- [ ] Test với image thực tế
- [ ] Kiểm tra logs trong production

## 🎯 Kết Quả Mong Đợi

Sau khi fix, chức năng Remove Background sẽ:
1. ✅ Gọi Runware API với đúng format
2. ✅ Xử lý response đúng cách
3. ✅ Có logs chi tiết để debug
4. ✅ Error handling tốt hơn
5. ✅ Trả về URL của image đã remove background

## 📚 Tài Liệu Tham Khảo

- [Runware Background Removal API](https://runware.ai/docs/en/image-editing/background-removal)
- [Runware Authentication](https://runware.ai/docs/en/getting-started/how-to-connect)
- [API Reference](https://runware.ai/docs/en/image-inference/api-reference)
