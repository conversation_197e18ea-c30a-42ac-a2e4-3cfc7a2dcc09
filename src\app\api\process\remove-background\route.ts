import { NextRequest, NextResponse } from "next/server";
import { getProcessedImage, updateProcessedImage, checkDuplicateRecords, getDatabaseStats } from "@/lib/supabase";
import { removeBackground } from "@/lib/runware";
import { ApiResponse } from "@/types";

export async function POST(request: NextRequest) {
  console.log(`[API] POST /api/process/remove-background - Request received`);

  try {
    const requestBody = await request.json();
    console.log(`[API] Request body:`, requestBody);

    const { imageUrl, imageId } = requestBody;

    // Accept either imageUrl (new simplified way) or imageId (legacy way)
    if (!imageUrl && !imageId) {
      console.error(`[API] Missing imageUrl or imageId in request`);
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Image URL or Image ID is required",
      }, { status: 400 });
    }

    let sourceImageUrl: string;
    let recordId: string | null = null;

    if (imageUrl) {
      // New simplified way - use URL directly (recommended)
      console.log(`[API] Using direct image URL approach`);
      sourceImageUrl = imageUrl;

      // Validate URL format
      try {
        new URL(imageUrl);
        console.log(`[API] Valid image URL: ${imageUrl}`);
      } catch {
        console.error(`[API] Invalid URL format: ${imageUrl}`);
        return NextResponse.json<ApiResponse>({
          success: false,
          error: "Invalid image URL format",
        }, { status: 400 });
      }
    } else {
      // Legacy way - query database for imageId (fallback)
      console.log(`[API] Using legacy imageId approach for imageId: ${imageId}`);

      // Check database health before proceeding
      console.log(`[API] Checking database health...`);
      await getDatabaseStats();
      await checkDuplicateRecords();

      // Get image record from database
      console.log(`[API] Fetching image record from database...`);
      const imageRecord = await getProcessedImage(imageId!);

      if (!imageRecord) {
        console.error(`[API] Image not found in database for imageId: ${imageId}`);
        return NextResponse.json<ApiResponse>({
          success: false,
          error: "Image not found",
        }, { status: 404 });
      }

      console.log(`[API] Image record found:`, {
        id: imageRecord.id,
        original_url: imageRecord.original_url,
        status: imageRecord.status
      });

      sourceImageUrl = imageRecord.original_url;
      recordId = imageId!;

      // Update status to processing
      console.log(`[API] Updating status to processing_background_removal...`);
      await updateProcessedImage(imageId!, {
        status: "processing_background_removal",
      });
    }

    try {
      // Remove background using Runware API
      console.log(`[API] Calling Runware API for background removal...`);
      console.log(`[API] Source image URL: ${sourceImageUrl}`);

      const backgroundRemovedUrl = await removeBackground(sourceImageUrl);

      console.log(`[API] Background removal successful. New URL: ${backgroundRemovedUrl}`);

      // Update database with result (only if we have recordId)
      if (recordId) {
        console.log(`[API] Updating database with result...`);
        const updatedRecord = await updateProcessedImage(recordId, {
          background_removed_url: backgroundRemovedUrl,
          status: "background_removed",
        });
        console.log(`[API] Database updated successfully`);
      } else {
        console.log(`[API] Skipping database update (direct URL mode)`);
      }

      const response = {
        success: true,
        data: {
          imageId: recordId,
          backgroundRemovedUrl: backgroundRemovedUrl,
          originalUrl: sourceImageUrl,
        },
        message: "Background removed successfully",
      };

      console.log(`[API] Sending success response:`, response);
      return NextResponse.json<ApiResponse>(response);

    } catch (runwareError) {
      console.error(`[API] Runware API error:`, runwareError);

      // Update status to error (only if we have recordId)
      if (recordId) {
        console.log(`[API] Updating status to error...`);
        await updateProcessedImage(recordId, {
          status: "error",
        });
      }

      throw runwareError;
    }

  } catch (error) {
    console.error("[API] Remove background error:", error);

    // Log detailed error information
    if (error instanceof Error) {
      console.error("[API] Error message:", error.message);
      console.error("[API] Error stack:", error.stack);
    }

    const errorResponse = {
      success: false,
      error: error instanceof Error ? error.message : "Background removal failed",
    };

    console.log("[API] Sending error response:", errorResponse);

    return NextResponse.json<ApiResponse>(errorResponse, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json<ApiResponse>({
    success: false,
    error: "Method not allowed",
  }, { status: 405 });
}
