import { RunwareTask, RunwareResponse } from '@/types';
import { randomUUID } from 'crypto';

const RUNWARE_API_KEY = process.env.RUNWARE_API_KEY!;
const RUNWARE_API_URL = process.env.RUNWARE_API_URL || 'https://api.runware.ai';

// Helper function to generate UUID that works in both browser and Node.js
function generateUUID(): string {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  } else if (typeof randomUUID !== 'undefined') {
    return randomUUID();
  } else {
    // Fallback UUID generation
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
}

class RunwareClient {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl: string) {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  private async makeRequest(tasks: RunwareTask[]): Promise<RunwareResponse[]> {
    console.log(`[Runware] Making request to ${this.baseUrl}/v1`);
    console.log(`[Runware] Request payload:`, JSON.stringify(tasks, null, 2));

    const response = await fetch(`${this.baseUrl}/v1`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify(tasks),
    });

    console.log(`[Runware] Response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[Runware] Error response:`, errorText);
      throw new Error(`Runware API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();
    console.log(`[Runware] Response data:`, JSON.stringify(data, null, 2));

    // Runware API returns data in { data: [...] } format
    if (data.data && Array.isArray(data.data)) {
      return data.data;
    }

    // If there's an error in the response
    if (data.errors && Array.isArray(data.errors)) {
      const errorMessage = data.errors.map((err: any) => err.message).join(', ');
      throw new Error(`Runware API error: ${errorMessage}`);
    }

    // Fallback - return as is if structure is unexpected
    return Array.isArray(data) ? data : [data];
  }

  async removeBackground(imageUrl: string): Promise<string> {
    // Generate unique UUID for this task
    const taskUUID = generateUUID();

    console.log(`[Runware] Starting background removal for image: ${imageUrl}`);
    console.log(`[Runware] Task UUID: ${taskUUID}`);

    // Validate image URL
    if (!imageUrl || typeof imageUrl !== 'string') {
      throw new Error('Invalid image URL provided');
    }

    // Create task according to Runware API docs
    const task: RunwareTask = {
      taskType: 'imageBackgroundRemoval',
      taskUUID: taskUUID,
      inputImage: imageUrl,
      outputFormat: 'PNG',
      outputType: 'URL',
      model: 'runware:109@1', // RemBG 1.4 model as per docs
      includeCost: false, // Optional: set to true if you want cost info
    };

    console.log(`[Runware] Sending request with task:`, JSON.stringify(task, null, 2));

    try {
      const results = await this.makeRequest([task]);
      console.log(`[Runware] Received response:`, JSON.stringify(results, null, 2));

      if (!results || results.length === 0) {
        console.error(`[Runware] Empty results array returned`);
        throw new Error('No results returned from Runware API');
      }

      const result = results[0];
      console.log(`[Runware] Processing result:`, result);

      // Check for errors in the result
      if (result.error) {
        console.error(`[Runware] API returned error:`, result.error);
        throw new Error(`Background removal failed: ${result.error}`);
      }

      // Check for imageURL in the response (as per Runware docs)
      if (!result.imageURL) {
        console.error(`[Runware] No imageURL in response:`, result);
        console.error(`[Runware] Available properties:`, Object.keys(result));

        // Check for alternative response fields
        if (result.imageBase64Data) {
          console.warn(`[Runware] Found imageBase64Data instead of imageURL`);
          throw new Error('Received base64 data instead of URL - check outputType parameter');
        }

        if (result.imageDataURI) {
          console.warn(`[Runware] Found imageDataURI instead of imageURL`);
          throw new Error('Received data URI instead of URL - check outputType parameter');
        }

        throw new Error('No image URL returned from background removal');
      }

      // Validate the returned URL
      if (typeof result.imageURL !== 'string' || !result.imageURL.startsWith('http')) {
        console.error(`[Runware] Invalid imageURL format:`, result.imageURL);
        throw new Error('Invalid image URL format returned from API');
      }

      console.log(`[Runware] Background removal successful. Result URL: ${result.imageURL}`);
      console.log(`[Runware] Task UUID: ${result.taskUUID}`);
      console.log(`[Runware] Image UUID: ${result.imageUUID}`);

      return result.imageURL;
    } catch (error) {
      console.error(`[Runware] Background removal error:`, error);

      // Re-throw with more context if it's our error
      if (error instanceof Error) {
        throw new Error(`Runware background removal failed: ${error.message}`);
      }

      throw new Error('Runware background removal failed: Unknown error');
    }
  }

  async upscaleImage(imageUrl: string, scale: number = 2): Promise<string> {
    // Generate unique UUID for this task
    const taskUUID = generateUUID();

    console.log(`[Runware] Starting image upscaling for image: ${imageUrl}`);
    console.log(`[Runware] Task UUID: ${taskUUID}`);

    const task: RunwareTask = {
      taskType: 'upscale',
      taskUUID: taskUUID,
      inputImage: imageUrl,
      outputFormat: 'PNG',
      outputType: 'URL',
      // Add upscale specific parameters
      ...{
        upscaleFactor: scale,
        model: 'RealESRGAN_x2plus'
      }
    };

    console.log(`[Runware] Sending upscale request with task:`, JSON.stringify(task, null, 2));

    const results = await this.makeRequest([task]);
    const result = results[0];

    if (result.error) {
      throw new Error(`Image upscaling failed: ${result.error}`);
    }

    if (!result.imageURL) {
      throw new Error('No image URL returned from upscaling');
    }

    return result.imageURL;
  }

  async generateBackground(
    productImageUrl: string,
    prompt: string = "professional product photography background, clean, modern, high quality"
  ): Promise<string> {
    // Generate unique UUID for this task
    const taskUUID = generateUUID();

    console.log(`[Runware] Starting background generation for image: ${productImageUrl}`);
    console.log(`[Runware] Task UUID: ${taskUUID}`);
    console.log(`[Runware] Prompt: ${prompt}`);

    const task: RunwareTask = {
      taskType: 'imageInference',
      taskUUID: taskUUID,
      inputImage: productImageUrl,
      outputFormat: 'PNG',
      outputType: 'URL',
      // Add background generation specific parameters
      ...{
        positivePrompt: prompt,
        negativePrompt: "blurry, low quality, distorted, ugly, bad anatomy, extra limbs",
        model: "runware:100@1",
        steps: 25,
        CFGScale: 7,
        seed: Math.floor(Math.random() * 1000000),
        width: 1024,
        height: 1024,
        scheduler: "DPM++ 2M Karras",
        lora: [
          {
            model: "product_photography_lora",
            weight: 0.8
          }
        ]
      }
    };

    console.log(`[Runware] Sending background generation request with task:`, JSON.stringify(task, null, 2));

    const results = await this.makeRequest([task]);
    const result = results[0];

    if (result.error) {
      throw new Error(`Background generation failed: ${result.error}`);
    }

    if (!result.imageURL) {
      throw new Error('No image URL returned from background generation');
    }

    return result.imageURL;
  }

  async enhanceImage(
    imageUrl: string,
    prompt: string = "high quality, sharp details, professional photography, 8k resolution"
  ): Promise<string> {
    // Generate unique UUID for this task
    const taskUUID = generateUUID();

    console.log(`[Runware] Starting image enhancement for image: ${imageUrl}`);
    console.log(`[Runware] Task UUID: ${taskUUID}`);
    console.log(`[Runware] Enhancement prompt: ${prompt}`);

    const task: RunwareTask = {
      taskType: 'imageInference',
      taskUUID: taskUUID,
      inputImage: imageUrl,
      outputFormat: 'PNG',
      outputType: 'URL',
      // Add enhancement specific parameters
      ...{
        positivePrompt: prompt,
        negativePrompt: "blurry, low quality, noise, artifacts, distorted",
        model: "runware:100@1",
        steps: 30,
        CFGScale: 7.5,
        seed: Math.floor(Math.random() * 1000000),
        width: 1024,
        height: 1024,
        scheduler: "DPM++ 2M Karras",
        strength: 0.3, // Lower strength to preserve original image
        lora: [
          {
            model: "detail_enhancement_lora",
            weight: 0.6
          }
        ]
      }
    };

    console.log(`[Runware] Sending enhancement request with task:`, JSON.stringify(task, null, 2));

    const results = await this.makeRequest([task]);
    const result = results[0];

    if (result.error) {
      throw new Error(`Image enhancement failed: ${result.error}`);
    }

    if (!result.imageURL) {
      throw new Error('No image URL returned from enhancement');
    }

    return result.imageURL;
  }
}

// Export singleton instance
export const runwareClient = new RunwareClient(RUNWARE_API_KEY, RUNWARE_API_URL);

// Export individual functions for easier use
export const removeBackground = (imageUrl: string) => runwareClient.removeBackground(imageUrl);
export const upscaleImage = (imageUrl: string, scale?: number) => runwareClient.upscaleImage(imageUrl, scale);
export const generateBackground = (productImageUrl: string, prompt?: string) =>
  runwareClient.generateBackground(productImageUrl, prompt);
export const enhanceImage = (imageUrl: string, prompt?: string) =>
  runwareClient.enhanceImage(imageUrl, prompt);
