import { NextRequest, NextResponse } from "next/server";
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { ApiResponse, UploadResponse } from "@/types";

export async function POST(request: NextRequest) {
  try {
    // Create Supabase client with cookies
    const cookieStore = await cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          },
        },
      }
    );

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Authentication required",
      }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const url = formData.get("url") as string;

    if (!file && !url) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "No file or URL provided",
      }, { status: 400 });
    }

    let uploadFile: File;

    if (file) {
      // Validate file
      if (!file.type.startsWith("image/")) {
        return NextResponse.json<ApiResponse>({
          success: false,
          error: "File must be an image",
        }, { status: 400 });
      }

      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        return NextResponse.json<ApiResponse>({
          success: false,
          error: "File size must be less than 10MB",
        }, { status: 400 });
      }

      uploadFile = file;
    } else {
      // Download from URL
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error("Failed to fetch image from URL");
        }

        const contentType = response.headers.get("content-type");
        if (!contentType?.startsWith("image/")) {
          return NextResponse.json<ApiResponse>({
            success: false,
            error: "URL does not point to an image",
          }, { status: 400 });
        }

        const blob = await response.blob();
        if (blob.size > 10 * 1024 * 1024) {
          return NextResponse.json<ApiResponse>({
            success: false,
            error: "Image size must be less than 10MB",
          }, { status: 400 });
        }

        uploadFile = new File([blob], "image-from-url", { type: blob.type });
      } catch {
        return NextResponse.json<ApiResponse>({
          success: false,
          error: "Failed to download image from URL",
        }, { status: 400 });
      }
    }

    // Generate unique filename
    const timestamp = Date.now();
    const extension = uploadFile.name.split(".").pop() || "jpg";
    const uniqueFileName = `original/${timestamp}-${Math.random().toString(36).substring(7)}.${extension}`;

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from("images")
      .upload(uniqueFileName, uploadFile, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      throw new Error(`Upload failed: ${uploadError.message}`);
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from("images")
      .getPublicUrl(uniqueFileName);

    // Save to database
    const { data: imageRecord, error: dbError } = await supabase
      .from('processed_images')
      .insert({
        original_url: publicUrl,
        status: "uploaded",
      })
      .select()
      .single();

    if (dbError) {
      throw new Error(`Database insert failed: ${dbError.message}`);
    }

    const response: UploadResponse = {
      imageId: imageRecord.id,
      url: uploadData.path,
      publicUrl: publicUrl,
    };

    return NextResponse.json<ApiResponse<UploadResponse>>({
      success: true,
      data: response,
      message: "Image uploaded successfully",
    });

  } catch (error) {
    console.error("Upload error:", error);

    return NextResponse.json<ApiResponse>({
      success: false,
      error: error instanceof Error ? error.message : "Upload failed",
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json<ApiResponse>({
    success: false,
    error: "Method not allowed",
  }, { status: 405 });
}
