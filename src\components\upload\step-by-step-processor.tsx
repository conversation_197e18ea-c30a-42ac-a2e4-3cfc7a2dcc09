"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Upload,
  Scissors,
  Zap,
  Palette,
  CheckCircle,
  Loader2,
  ExternalLink,
  AlertCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface StepState {
  status: 'idle' | 'processing' | 'completed' | 'error';
  result?: string;
  error?: string;
}

interface WorkflowState {
  upload: StepState;
  removeBackground: StepState;
  upscale: StepState;
  generateBackground: StepState;
  finalize: StepState;
  currentImageId?: string;
}

interface StepByStepProcessorProps {
  selectedFile?: File;
  selectedUrl?: string;
  onComplete?: (result: any) => void;
  onReset?: () => void;
}

export function StepByStepProcessor({
  selectedFile,
  selectedUrl,
  onComplete,
  onReset
}: StepByStepProcessorProps) {
  const { toast } = useToast();

  const [workflow, setWorkflow] = useState<WorkflowState>({
    upload: { status: 'idle' },
    removeBackground: { status: 'idle' },
    upscale: { status: 'idle' },
    generateBackground: { status: 'idle' },
    finalize: { status: 'idle' }
  });

  const updateStep = (step: keyof WorkflowState, update: Partial<StepState>) => {
    setWorkflow(prev => ({
      ...prev,
      [step]: { ...prev[step], ...update }
    }));
  };

  const handleUpload = async () => {
    if (!selectedFile && !selectedUrl) {
      toast({
        title: "Lỗi",
        description: "Vui lòng chọn file hoặc nhập URL",
        variant: "destructive"
      });
      return;
    }

    updateStep('upload', { status: 'processing', error: undefined });

    try {
      const formData = new FormData();
      if (selectedFile) {
        formData.append("file", selectedFile);
      } else if (selectedUrl) {
        formData.append("url", selectedUrl);
      }

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Upload failed");
      }

      const result = await response.json();

      setWorkflow(prev => ({ ...prev, currentImageId: result.data.imageId }));
      updateStep('upload', {
        status: 'completed',
        result: result.data.publicUrl
      });

      toast({
        title: "Thành công",
        description: "Đã tải lên hình ảnh thành công",
      });

    } catch (error) {
      updateStep('upload', {
        status: 'error',
        error: error instanceof Error ? error.message : "Upload failed"
      });

      toast({
        title: "Lỗi",
        description: "Không thể tải lên hình ảnh",
        variant: "destructive"
      });
    }
  };

  const handleRemoveBackground = async () => {
    // Use direct URL approach (new simplified way)
    const imageUrl = workflow.upload.result;

    if (!imageUrl) {
      toast({
        title: "Lỗi",
        description: "Không tìm thấy URL hình ảnh. Vui lòng upload lại.",
        variant: "destructive"
      });
      return;
    }

    updateStep('removeBackground', { status: 'processing', error: undefined });

    try {
      console.log(`[Frontend] Calling remove-background with URL: ${imageUrl}`);

      const response = await fetch("/api/process/remove-background", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          imageUrl: imageUrl,
          // Keep imageId as fallback for legacy support
          imageId: workflow.currentImageId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Background removal failed");
      }

      const result = await response.json();
      console.log(`[Frontend] Remove background success:`, result);

      updateStep('removeBackground', {
        status: 'completed',
        result: result.data.backgroundRemovedUrl
      });

      toast({
        title: "Thành công",
        description: "Đã xóa background thành công",
      });

    } catch (error) {
      console.error(`[Frontend] Remove background error:`, error);

      updateStep('removeBackground', {
        status: 'error',
        error: error instanceof Error ? error.message : "Background removal failed"
      });

      toast({
        title: "Lỗi",
        description: "Không thể xóa background",
        variant: "destructive"
      });
    }
  };

  const handleUpscale = async () => {
    if (!workflow.currentImageId) return;

    updateStep('upscale', { status: 'processing', error: undefined });

    try {
      const response = await fetch("/api/process/upscale", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ imageId: workflow.currentImageId }),
      });

      if (!response.ok) {
        throw new Error("Upscale failed");
      }

      const result = await response.json();

      updateStep('upscale', {
        status: 'completed',
        result: result.data.upscaledUrl
      });

      toast({
        title: "Thành công",
        description: "Đã tăng độ phân giải thành công",
      });

    } catch (error) {
      updateStep('upscale', {
        status: 'error',
        error: error instanceof Error ? error.message : "Upscale failed"
      });

      toast({
        title: "Lỗi",
        description: "Không thể tăng độ phân giải",
        variant: "destructive"
      });
    }
  };

  const handleGenerateBackground = async () => {
    if (!workflow.currentImageId) return;

    updateStep('generateBackground', { status: 'processing', error: undefined });

    try {
      const response = await fetch("/api/process/generate-background", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ imageId: workflow.currentImageId }),
      });

      if (!response.ok) {
        throw new Error("Background generation failed");
      }

      const result = await response.json();

      updateStep('generateBackground', {
        status: 'completed',
        result: result.data.finalUrl
      });

      toast({
        title: "Thành công",
        description: "Đã tạo background mới thành công",
      });

    } catch (error) {
      updateStep('generateBackground', {
        status: 'error',
        error: error instanceof Error ? error.message : "Background generation failed"
      });

      toast({
        title: "Lỗi",
        description: "Không thể tạo background mới",
        variant: "destructive"
      });
    }
  };

  const handleFinalize = async () => {
    if (!workflow.currentImageId) return;

    updateStep('finalize', { status: 'processing', error: undefined });

    try {
      // Get final result from database
      const response = await fetch(`/api/process/result/${workflow.currentImageId}`);

      if (!response.ok) {
        throw new Error("Failed to get final result");
      }

      const result = await response.json();

      updateStep('finalize', { status: 'completed' });

      if (onComplete) {
        onComplete(result.data);
      }

      toast({
        title: "Hoàn thành",
        description: "Đã hoàn thiện xử lý hình ảnh",
      });

    } catch (error) {
      updateStep('finalize', {
        status: 'error',
        error: error instanceof Error ? error.message : "Finalization failed"
      });

      toast({
        title: "Lỗi",
        description: "Không thể hoàn thiện xử lý",
        variant: "destructive"
      });
    }
  };

  const steps = [
    {
      key: 'upload' as const,
      title: 'Tải lên hình ảnh',
      icon: Upload,
      description: 'Upload file hoặc URL',
      action: handleUpload,
      disabled: false
    },
    {
      key: 'removeBackground' as const,
      title: 'Xóa background',
      icon: Scissors,
      description: 'Loại bỏ background cũ',
      action: handleRemoveBackground,
      disabled: workflow.upload.status !== 'completed'
    },
    {
      key: 'upscale' as const,
      title: 'Tăng độ phân giải',
      icon: Zap,
      description: 'Cải thiện chất lượng hình ảnh',
      action: handleUpscale,
      disabled: workflow.removeBackground.status !== 'completed'
    },
    {
      key: 'generateBackground' as const,
      title: 'Tạo background mới',
      icon: Palette,
      description: 'Tạo background chuyên nghiệp',
      action: handleGenerateBackground,
      disabled: workflow.upscale.status !== 'completed'
    },
    {
      key: 'finalize' as const,
      title: 'Hoàn thiện',
      icon: CheckCircle,
      description: 'Hoàn tất và hiển thị kết quả',
      action: handleFinalize,
      disabled: workflow.generateBackground.status !== 'completed'
    }
  ];

  const getStatusBadge = (status: StepState['status']) => {
    switch (status) {
      case 'idle':
        return <Badge variant="secondary">Chờ</Badge>;
      case 'processing':
        return <Badge variant="default">Đang xử lý</Badge>;
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Hoàn thành</Badge>;
      case 'error':
        return <Badge variant="destructive">Lỗi</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">Xử Lý Từng Bước</h2>
        <p className="text-muted-foreground">Thực hiện từng bước một cách thủ công để kiểm soát quá trình</p>
      </div>

      <div className="grid gap-4">
        {steps.map((step, index) => {
          const stepState = workflow[step.key];
          const Icon = step.icon;

          return (
            <Card key={step.key} className={stepState.status === 'completed' ? 'border-green-200' : ''}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Icon className="h-5 w-5" />
                    {index + 1}. {step.title}
                  </CardTitle>
                  {getStatusBadge(stepState.status)}
                </div>
                <p className="text-sm text-muted-foreground">{step.description}</p>
              </CardHeader>

              <CardContent className="space-y-3">
                <Button
                  onClick={step.action}
                  disabled={step.disabled || stepState.status === 'processing'}
                  className="w-full"
                >
                  {stepState.status === 'processing' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Đang xử lý...
                    </>
                  ) : (
                    step.title
                  )}
                </Button>

                {stepState.result && (
                  <Alert>
                    <ExternalLink className="h-4 w-4" />
                    <AlertDescription className="flex items-center justify-between">
                      <span>Kết quả: </span>
                      <Button
                        variant="link"
                        size="sm"
                        onClick={() => window.open(stepState.result, '_blank')}
                        className="p-0 h-auto"
                      >
                        Xem kết quả
                      </Button>
                    </AlertDescription>
                  </Alert>
                )}

                {stepState.error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{stepState.error}</AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {workflow.finalize.status === 'completed' && onReset && (
        <div className="text-center">
          <Button variant="outline" onClick={onReset}>
            Bắt đầu lại
          </Button>
        </div>
      )}
    </div>
  );
}
