import { createClient } from '@supabase/supabase-js';
import { createBrowserClient } from '@supabase/ssr';
import { Database } from '@/types';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Client for browser/frontend with SSR support
export const supabase = createBrowserClient<Database>(supabaseUrl, supabaseAnonKey);

// Legacy client for compatibility
export const supabaseClient = createClient<Database>(supabaseUrl, supabaseAnonKey);

// Storage bucket name
export const STORAGE_BUCKET = 'images';

// Helper functions for file upload
export const uploadImage = async (file: File, path: string) => {
  const { data, error } = await supabase.storage
    .from(STORAGE_BUCKET)
    .upload(path, file, {
      cacheControl: '3600',
      upsert: false
    });

  if (error) {
    throw new Error(`Upload failed: ${error.message}`);
  }

  return data;
};

export const getPublicUrl = (path: string) => {
  const { data } = supabase.storage
    .from(STORAGE_BUCKET)
    .getPublicUrl(path);

  return data.publicUrl;
};

export const deleteImage = async (path: string) => {
  const { error } = await supabase.storage
    .from(STORAGE_BUCKET)
    .remove([path]);

  if (error) {
    throw new Error(`Delete failed: ${error.message}`);
  }
};

// Database operations
export const saveProcessedImage = async (imageData: Database['public']['Tables']['processed_images']['Insert']) => {
  const { data, error } = await supabase
    .from('processed_images')
    .insert(imageData)
    .select()
    .single();

  if (error) {
    throw new Error(`Database insert failed: ${error.message}`);
  }

  return data;
};

export const updateProcessedImage = async (
  id: string,
  updates: Database['public']['Tables']['processed_images']['Update']
) => {
  const { data, error } = await supabase
    .from('processed_images')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Database update failed: ${error.message}`);
  }

  return data;
};

export const getProcessedImage = async (id: string) => {
  console.log(`[Database] Fetching processed image with ID: ${id}`);

  // Validate ID format (should be UUID)
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(id)) {
    console.error(`[Database] Invalid UUID format: ${id}`);
    throw new Error(`Invalid image ID format: ${id}`);
  }

  // First, check how many records exist with this ID
  const { data: countData, error: countError } = await supabase
    .from('processed_images')
    .select('id', { count: 'exact' })
    .eq('id', id);

  if (countError) {
    console.error(`[Database] Count query failed:`, countError);
    throw new Error(`Database count failed: ${countError.message}`);
  }

  console.log(`[Database] Found ${countData?.length || 0} records with ID: ${id}`);

  if (!countData || countData.length === 0) {
    console.error(`[Database] No records found with ID: ${id}`);
    throw new Error(`Image not found with ID: ${id}`);
  }

  if (countData.length > 1) {
    console.error(`[Database] Multiple records found with ID: ${id}. Count: ${countData.length}`);
    // For now, we'll take the first one and log this issue
    console.warn(`[Database] Taking the first record, but this indicates a data integrity issue`);
  }

  // Get the actual data
  const { data, error } = await supabase
    .from('processed_images')
    .select('*')
    .eq('id', id)
    .limit(1)
    .single();

  if (error) {
    console.error(`[Database] Select query failed:`, error);
    throw new Error(`Database select failed: ${error.message}`);
  }

  if (!data) {
    console.error(`[Database] No data returned for ID: ${id}`);
    throw new Error(`No data found for image ID: ${id}`);
  }

  console.log(`[Database] Successfully fetched image:`, {
    id: data.id,
    original_url: data.original_url,
    status: data.status,
    created_at: data.created_at
  });

  return data;
};

export const getAllProcessedImages = async (limit = 50, offset = 0) => {
  const { data, error } = await supabase
    .from('processed_images')
    .select('*')
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw new Error(`Database select failed: ${error.message}`);
  }

  return data;
};

// Helper function to check for duplicate records
export const checkDuplicateRecords = async () => {
  console.log(`[Database] Checking for duplicate records...`);

  const { data, error } = await supabase
    .from('processed_images')
    .select('id, original_url, created_at')
    .order('created_at', { ascending: false });

  if (error) {
    console.error(`[Database] Failed to check duplicates:`, error);
    return;
  }

  // Group by ID to find duplicates
  const idGroups: { [key: string]: any[] } = {};
  data?.forEach(record => {
    if (!idGroups[record.id]) {
      idGroups[record.id] = [];
    }
    idGroups[record.id].push(record);
  });

  // Find duplicates
  const duplicates = Object.entries(idGroups).filter(([id, records]) => records.length > 1);

  if (duplicates.length > 0) {
    console.warn(`[Database] Found ${duplicates.length} duplicate IDs:`);
    duplicates.forEach(([id, records]) => {
      console.warn(`[Database] ID ${id} has ${records.length} records:`, records);
    });
  } else {
    console.log(`[Database] No duplicate records found`);
  }

  return {
    totalRecords: data?.length || 0,
    duplicateIds: duplicates.length,
    duplicates: duplicates
  };
};

// Helper function to get database statistics
export const getDatabaseStats = async () => {
  console.log(`[Database] Getting database statistics...`);

  const { data, error } = await supabase
    .from('processed_images')
    .select('id, status, created_at')
    .order('created_at', { ascending: false });

  if (error) {
    console.error(`[Database] Failed to get stats:`, error);
    return null;
  }

  const stats = {
    total: data?.length || 0,
    byStatus: {} as { [key: string]: number },
    recent: data?.slice(0, 5) || []
  };

  data?.forEach(record => {
    const status = record.status || 'unknown';
    stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;
  });

  console.log(`[Database] Stats:`, stats);
  return stats;
};
